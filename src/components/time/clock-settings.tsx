"use client";

import { useState, useEffect } from "react";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

import { Settings, X } from "lucide-react";

interface ClockSettingsProps {
  showSeconds: boolean;
  setShowSeconds: (value: boolean) => void;
  showWeekday: boolean;
  setShowWeekday: (value: boolean) => void;
  showDate: boolean;
  setShowDate: (value: boolean) => void;
  showWeekNumber: boolean;
  setShowWeekNumber: (value: boolean) => void;
  use12Hours?: boolean;
  setUse12Hours?: (value: boolean) => void;
  textColor: string;
  setTextColor: (value: string) => void;
  fontSize: string;
  setFontSize: (value: string) => void;
  fontFamily: string;
  setFontFamily: (value: string) => void;
  position?: { x: number; y: number };
  setPosition?: (value: { x: number; y: number }) => void;
  backgroundColor?: string;
  setBackgroundColor?: (value: string) => void;
  backgroundImage?: string;
  setBackgroundImage?: (value: string) => void;
}

export function ClockSettings({
  showSeconds,
  setShowSeconds,
  showWeekday,
  setShowWeekday,
  showDate,
  setShowDate,
  showWeekNumber,
  setShowWeekNumber,
  use12Hours = false,
  setUse12Hours = () => {},
  textColor,
  setTextColor,
  fontSize,
  setFontSize,
  fontFamily,
  setFontFamily,
  position = { x: 0, y: 0 },
  setPosition = () => {},
  backgroundColor = "",
  setBackgroundColor = () => {},
  backgroundImage = "",
  setBackgroundImage = () => {},
}: ClockSettingsProps) {
  const [fontSizeValue, setFontSizeValue] = useState(() => parseInt(fontSize));
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Set mounted state
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Update fontSizeValue when fontSize prop changes (but prevent infinite loops)
  useEffect(() => {
    const newValue = parseInt(fontSize);
    if (newValue !== fontSizeValue) {
      setFontSizeValue(newValue);
    }
  }, [fontSize]);

  // Update fontSize when fontSizeValue changes (with debounce to prevent infinite loops)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const newFontSize = `${fontSizeValue}rem`;
      if (newFontSize !== fontSize) {
        setFontSize(newFontSize);
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [fontSizeValue]);

  // Handle fullscreen changes (only after mount)
  useEffect(() => {
    if (!isMounted) return;

    const handleFullScreenChange = () => {
      if (document.fullscreenElement) {
        setIsCollapsed(true);
      }
    };

    document.addEventListener('fullscreenchange', handleFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
    };
  }, [isMounted]);

  const fontOptions = [
    { value: "monospace", label: "Monospace" },
    { value: "sans-serif", label: "Sans Serif" },
    { value: "serif", label: "Serif" },
    { value: "Arial", label: "Arial" },
    { value: "Verdana", label: "Verdana" },
    { value: "Helvetica", label: "Helvetica" },
    { value: "Times New Roman", label: "Times New Roman" },
    { value: "Courier New", label: "Courier New" },
  ];

  const handleMove = (direction: 'up' | 'down' | 'left' | 'right') => {
    const step = 20; // pixels to move per click
    const newPosition = { ...position };

    switch (direction) {
      case 'up':
        newPosition.y -= step;
        break;
      case 'down':
        newPosition.y += step;
        break;
      case 'left':
        newPosition.x -= step;
        break;
      case 'right':
        newPosition.x += step;
        break;
    }

    setPosition(newPosition);
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div
      className={`bg-white dark:bg-gray-900 rounded-lg shadow-md transition-all duration-300 ease-in-out ${
        isCollapsed
          ? 'w-12 h-12 p-0 overflow-hidden'
          : 'w-full max-w-96 h-[90vh] flex flex-col'
      }`}
    >
      {isCollapsed ? (
        <button
          onClick={toggleCollapse}
          className="w-full h-full flex items-center justify-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
        >
          <Settings size={24} />
        </button>
      ) : (
        <>
          {/* Header */}
          <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
            <h2 className="text-xl font-bold">Clock Settings</h2>
            <button
              onClick={toggleCollapse}
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <X size={20} />
            </button>
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto px-4 py-2">
            <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Display Options</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showSeconds"
                    checked={showSeconds}
                    onChange={(e) => setShowSeconds(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showSeconds">Show Seconds</label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="use12Hours"
                    checked={use12Hours}
                    onChange={(e) => setUse12Hours(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="use12Hours">12 Hours (AM/PM)</label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showWeekday"
                    checked={showWeekday}
                    onChange={(e) => setShowWeekday(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showWeekday">Show Weekday</label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showDate"
                    checked={showDate}
                    onChange={(e) => setShowDate(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showDate">Show Date</label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showWeekNumber"
                    checked={showWeekNumber}
                    onChange={(e) => setShowWeekNumber(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showWeekNumber">Show Week Number</label>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Background</h3>
              <div className="space-y-4">
                <div>
                  <label htmlFor="backgroundColor" className="block mb-1">Background Color</label>
                  <div className="flex gap-2">
                    <input
                      type="color"
                      id="backgroundColor"
                      value={backgroundColor || "#ffffff"}
                      onChange={(e) => {
                        console.log("Setting background color from picker:", e.target.value);
                        setBackgroundColor(e.target.value);
                        setBackgroundImage('');
                      }}
                      className="w-full h-10 rounded"
                    />
                    <button
                      onClick={() => {
                        console.log("Clearing background color");
                        setBackgroundColor('');
                      }}
                      className="px-2 py-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm"
                    >
                      Clear
                    </button>
                  </div>
                </div>

                <div>
                  <label htmlFor="backgroundImage" className="block mb-1">Background Image URL</label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      id="backgroundImage"
                      value={backgroundImage}
                      onChange={(e) => {
                        console.log("Setting background image from URL:", e.target.value);
                        setBackgroundImage(e.target.value);
                        setBackgroundColor('');
                      }}
                      placeholder="Enter image URL"
                      className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
                    />
                    <button
                      onClick={() => {
                        console.log("Testing background image");
                        if (backgroundImage) {
                          // Test if the image URL is valid
                          const img = new Image();
                          img.onload = () => {
                            alert("Image loaded successfully!");
                          };
                          img.onerror = () => {
                            alert("Failed to load image. Please check the URL.");
                          };
                          img.src = backgroundImage;
                        }
                      }}
                      className="px-2 py-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm"
                    >
                      Test
                    </button>
                  </div>
                </div>

                <div>
                  <label htmlFor="backgroundImageUpload" className="block mb-1">Or Upload Local Image</label>
                  <input
                    type="file"
                    id="backgroundImageUpload"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        // Convert to base64 for persistent storage
                        const reader = new FileReader();
                        reader.onload = (event) => {
                          const base64String = event.target?.result as string;
                          console.log("Setting background image from file (base64):", base64String.substring(0, 50) + "...");
                          setBackgroundImage(base64String);
                          setBackgroundColor('');
                        };
                        reader.readAsDataURL(file);
                      }
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
                  />
                </div>

                <div className="flex justify-between mt-2">
                  <button
                    onClick={() => {
                      setBackgroundImage('');
                      setBackgroundColor('');
                      console.log("Cleared all background settings");
                    }}
                    className="px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-200 rounded text-sm"
                  >
                    Clear All
                  </button>

                  <button
                    onClick={() => {
                      // Apply a sample background image
                      const sampleImage = "https://source.unsplash.com/random/1920x1080/?nature";
                      console.log("Setting sample background image:", sampleImage);
                      setBackgroundImage(sampleImage);
                      setBackgroundColor('');
                    }}
                    className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 dark:bg-blue-900 dark:hover:bg-blue-800 dark:text-blue-200 rounded text-sm"
                  >
                    Sample Image
                  </button>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Appearance</h3>

              <div className="space-y-4">
                <div>
                  <label htmlFor="textColor" className="block mb-1">Text Color</label>
                  <input
                    type="color"
                    id="textColor"
                    value={textColor}
                    onChange={(e) => setTextColor(e.target.value)}
                    className="w-full h-10 rounded"
                  />
                </div>

                <div>
                  <label className="block mb-1">Font Size: {fontSizeValue}rem</label>
                  <Slider
                    value={[fontSizeValue]}
                    min={1}
                    max={12}
                    step={1}
                    onValueChange={(value) => setFontSizeValue(value[0])}
                    className="my-4"
                  />
                </div>

                <div>
                  <label htmlFor="fontFamily" className="block mb-1">Font Family</label>
                  <Select value={fontFamily} onValueChange={setFontFamily}>
                    <SelectTrigger id="fontFamily">
                      <SelectValue placeholder="Select font" />
                    </SelectTrigger>
                    <SelectContent>
                      {fontOptions.map((font) => (
                        <SelectItem key={font.value} value={font.value}>
                          {font.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Position Controls */}
            <div>
              <h3 className="text-lg font-medium mb-2">Clock Position</h3>
              <div className="space-y-3">
                <div className="grid grid-cols-3 gap-2">
                  <div></div>
                  <Button
                    variant="outline"
                    onClick={() => handleMove('up')}
                    className="w-full"
                  >
                    ↑
                  </Button>
                  <div></div>

                  <Button
                    variant="outline"
                    onClick={() => handleMove('left')}
                    className="w-full"
                  >
                    ←
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setPosition({ x: 0, y: 0 })}
                    className="w-full text-xs"
                  >
                    Center
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleMove('right')}
                    className="w-full"
                  >
                    →
                  </Button>

                  <div></div>
                  <Button
                    variant="outline"
                    onClick={() => handleMove('down')}
                    className="w-full"
                  >
                    ↓
                  </Button>
                  <div></div>
                </div>

                <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  Position: ({position?.x || 0}, {position?.y || 0})
                </div>
              </div>
            </div>
            </div>
          </div>

          {/* Bottom OK Button */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0">
            <button
              onClick={toggleCollapse}
              className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium"
            >
              OK
            </button>
          </div>
        </>
      )}
    </div>
  );
}
