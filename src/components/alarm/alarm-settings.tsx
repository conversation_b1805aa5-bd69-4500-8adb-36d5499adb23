"use client";

import { useState } from "react";
import { AlarmForm } from "./alarm-form";
import { Settings, X } from "lucide-react";

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
}

interface AlarmSettingsProps {
  onAddAlarm: (alarm: Alarm) => boolean;
  onTestAlarm: (alarm: Omit<Alarm, 'id' | 'enabled'>) => void;
  existingAlarms: Alarm[];
}

export function AlarmSettings({ onAddAlarm, onTestAlarm, existingAlarms }: AlarmSettingsProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div
      className={`bg-white dark:bg-gray-900 rounded-lg shadow-md transition-all duration-300 ease-in-out ${
        isCollapsed
          ? 'w-12 h-12 p-0 overflow-hidden'
          : 'w-full max-w-96 h-[90vh] flex flex-col'
      }`}
    >
      {isCollapsed ? (
        <button
          onClick={toggleCollapse}
          className="w-full h-full flex items-center justify-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
        >
          <Settings size={24} />
        </button>
      ) : (
        <>
          {/* Header */}
          <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
            <h2 className="text-xl font-bold">Alarm Settings</h2>
            <button
              onClick={toggleCollapse}
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <X size={20} />
            </button>
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto px-4 py-2">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4">Add New Alarm</h3>
                <AlarmForm
                  onAddAlarm={onAddAlarm}
                  onTestAlarm={onTestAlarm}
                  existingAlarms={existingAlarms}
                />
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Instructions</h3>
                <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                  <p>• Set the time for your alarm</p>
                  <p>• Choose a title to identify your alarm</p>
                  <p>• Select an alarm sound</p>
                  <p>• Your alarms will appear below the clock</p>
                  <p>• Toggle alarms on/off or delete them as needed</p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Tips</h3>
                <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                  <p>• Alarms work even when the browser tab is not active</p>
                  <p>• Make sure your device volume is turned up</p>
                  <p>• You can snooze alarms for 5, 10, or 15 minutes</p>
                  <p>• Disabled alarms won't ring but are saved for later</p>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom OK Button */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0">
            <button
              onClick={toggleCollapse}
              className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium"
            >
              OK
            </button>
          </div>
        </>
      )}
    </div>
  );
}
