"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { DigitalClock } from "@/components/time/digital-clock";
import { AlarmSettings } from "@/components/alarm/alarm-settings";
import { AlarmIndicator } from "@/components/alarm/alarm-indicator";
import { AlarmNotification } from "@/components/alarm/alarm-notification";

// Simple localStorage utilities for clock settings
const CLOCK_STORAGE_KEY = "clockSettings";

const defaultClockSettings = {
  showSeconds: true,
  showWeekday: true,
  showDate: true,
  showWeekNumber: true,
  use12Hours: false,
  textColor: "#000000",
  fontSize: "6rem",
  fontFamily: "monospace",
  position: { x: 0, y: 0 },
  backgroundColor: "",
  backgroundImage: "",
};

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
  isTemporary?: boolean; // For snooze alarms - not saved to localStorage
  originalId?: string;   // Reference to the original alarm that was snoozed
}

export default function AlarmPage() {
  const [alarms, setAlarms] = useState<Alarm[]>([]);
  const [activeAlarm, setActiveAlarm] = useState<Alarm | null>(null);
  const [isAlarmRinging, setIsAlarmRinging] = useState(false);
  const [clockSettings, setClockSettings] = useState(defaultClockSettings);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [alarmQueue, setAlarmQueue] = useState<Alarm[]>([]);
  const [isMounted, setIsMounted] = useState(false);

  // Set mounted state
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Load alarms from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedAlarms = localStorage.getItem("clockApp_alarms");
        console.log("Loading alarms from localStorage:", savedAlarms);
        if (savedAlarms && savedAlarms !== 'undefined' && savedAlarms !== 'null') {
          const parsedAlarms = JSON.parse(savedAlarms);
          console.log("Parsed alarms:", parsedAlarms);
          if (Array.isArray(parsedAlarms)) {
            setAlarms(parsedAlarms);
          }
        }
      } catch (error) {
        console.error("Error loading alarms from localStorage:", error);
      } finally {
        setIsLoaded(true);
      }
    }
  }, []);

  // Load clock settings from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(CLOCK_STORAGE_KEY);
        if (saved) {
          const parsed = JSON.parse(saved);
          setClockSettings({ ...defaultClockSettings, ...parsed });
        }
      } catch (error) {
        console.error("Error loading clock settings:", error);
      }
    }
  }, []);

  // Monitor fullscreen state
  useEffect(() => {
    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullScreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
    };
  }, []);

  // Save alarms to localStorage when they change (only after initial load, exclude temporary alarms)
  useEffect(() => {
    if (isLoaded && typeof window !== 'undefined') {
      try {
        // Only save permanent alarms (exclude temporary snooze alarms)
        const permanentAlarms = alarms.filter(alarm => !alarm.isTemporary);
        const alarmsString = JSON.stringify(permanentAlarms);
        localStorage.setItem("clockApp_alarms", alarmsString);
        console.log("Permanent alarms saved to localStorage:", alarmsString);
        console.log("Temporary alarms (not saved):", alarms.filter(alarm => alarm.isTemporary));

        // Also save to a backup key
        localStorage.setItem("clockApp_alarms_backup", alarmsString);
      } catch (error) {
        console.error("Error saving alarms to localStorage:", error);
      }
    }
  }, [alarms, isLoaded]);

  // Check for alarms that need to ring
  useEffect(() => {
    const checkAlarms = () => {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}`;

      // Find all alarms that should ring at this time
      const alarmsToRing = alarms.filter(
        (alarm) => alarm.enabled && alarm.time === currentTime && now.getSeconds() === 0
      );

      if (alarmsToRing.length > 0 && !isAlarmRinging) {
        // If multiple alarms, add them to queue and ring the first one
        if (alarmsToRing.length > 1) {
          setAlarmQueue(alarmsToRing.slice(1)); // Add remaining alarms to queue
          console.log(`Multiple alarms at ${currentTime}:`, alarmsToRing.map(a => a.title));
        }

        const firstAlarm = alarmsToRing[0];
        setActiveAlarm(firstAlarm);
        setIsAlarmRinging(true);

        // If this is a temporary alarm (snoozed), remove it after triggering
        if (firstAlarm.isTemporary) {
          console.log(`Removing temporary alarm after triggering: ${firstAlarm.title}`);
          setAlarms(prevAlarms => prevAlarms.filter(alarm => alarm.id !== firstAlarm.id));
        }
      }
    };

    const intervalId = setInterval(checkAlarms, 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, [alarms, isAlarmRinging]);

  const handleAddAlarm = (alarm: Alarm): boolean => {
    setAlarms((prevAlarms) => [...prevAlarms, alarm]);
    return true; // Return success
  };

  const handleTestAlarm = (alarmData: Omit<Alarm, 'id' | 'enabled'>) => {
    // Create a temporary alarm for testing
    const testAlarm: Alarm = {
      id: 'test',
      ...alarmData,
      enabled: true,
    };

    setActiveAlarm(testAlarm);
    setIsAlarmRinging(true);
  };

  const handleToggleAlarm = (id: string) => {
    setAlarms((prevAlarms) =>
      prevAlarms.map((alarm) =>
        alarm.id === id ? { ...alarm, enabled: !alarm.enabled } : alarm
      )
    );
  };

  const handleDeleteAlarm = (id: string) => {
    setAlarms((prevAlarms) => prevAlarms.filter((alarm) => alarm.id !== id));
  };

  const handleCloseAlarm = () => {
    const currentAlarm = activeAlarm;
    setIsAlarmRinging(false);
    setActiveAlarm(null);

    // If the dismissed alarm was temporary, remove it from the list
    if (currentAlarm?.isTemporary) {
      console.log(`Removing dismissed temporary alarm: ${currentAlarm.title}`);
      setAlarms(prevAlarms => prevAlarms.filter(alarm => alarm.id !== currentAlarm.id));
    }

    // Check if there are more alarms in the queue
    if (alarmQueue.length > 0) {
      // Ring the next alarm after a short delay
      setTimeout(() => {
        const nextAlarm = alarmQueue[0];
        setAlarmQueue(prev => prev.slice(1)); // Remove from queue
        setActiveAlarm(nextAlarm);
        setIsAlarmRinging(true);
        console.log(`Next alarm from queue: ${nextAlarm.title}`);

        // If the next alarm is also temporary, remove it from the main list
        if (nextAlarm.isTemporary) {
          setAlarms(prevAlarms => prevAlarms.filter(alarm => alarm.id !== nextAlarm.id));
        }
      }, 1000); // 1 second delay between alarms
    }
  };

  const handleSnoozeAlarm = (minutes: number) => {
    if (activeAlarm) {
      setIsAlarmRinging(false);

      // Calculate new alarm time
      const now = new Date();
      now.setMinutes(now.getMinutes() + minutes);

      const newTime = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}`;

      // Create temporary snoozed alarm
      const snoozedAlarm: Alarm = {
        ...activeAlarm,
        id: `snooze_${Date.now()}`,
        time: newTime,
        title: `${activeAlarm.title.replace(' (Snoozed)', '')} (Snoozed ${minutes}min)`,
        isTemporary: true, // Mark as temporary - won't be saved to localStorage
        originalId: activeAlarm.isTemporary ? activeAlarm.originalId : activeAlarm.id, // Track original alarm
      };

      // Add the temporary snoozed alarm
      setAlarms((prevAlarms) => [...prevAlarms, snoozedAlarm]);
      setActiveAlarm(null);

      console.log(`Temporary snoozed alarm created: ${snoozedAlarm.title} at ${newTime}`);
      console.log(`Original alarm ID: ${snoozedAlarm.originalId}, Temporary: ${snoozedAlarm.isTemporary}`);

      // Check if there are more alarms in the queue
      if (alarmQueue.length > 0) {
        // Ring the next alarm after a short delay
        setTimeout(() => {
          const nextAlarm = alarmQueue[0];
          setAlarmQueue(prev => prev.slice(1)); // Remove from queue
          setActiveAlarm(nextAlarm);
          setIsAlarmRinging(true);
          console.log(`Next alarm from queue after snooze: ${nextAlarm.title}`);
        }, 1000); // 1 second delay between alarms
      }
    }
  };

  // Apply background styles to the entire page
  useEffect(() => {
    if (typeof document === 'undefined') return;

    const htmlElement = document.documentElement;
    const bodyElement = document.body;

    // Apply background styles
    if (clockSettings.backgroundImage) {
      htmlElement.style.backgroundImage = `url("${clockSettings.backgroundImage}")`;
      bodyElement.style.backgroundImage = `url("${clockSettings.backgroundImage}")`;
      htmlElement.style.backgroundColor = '';
      bodyElement.style.backgroundColor = '';
    } else if (clockSettings.backgroundColor) {
      htmlElement.style.backgroundColor = clockSettings.backgroundColor;
      bodyElement.style.backgroundColor = clockSettings.backgroundColor;
      htmlElement.style.backgroundImage = 'none';
      bodyElement.style.backgroundImage = 'none';
    } else {
      htmlElement.style.backgroundImage = 'none';
      bodyElement.style.backgroundImage = 'none';
      htmlElement.style.backgroundColor = '';
      bodyElement.style.backgroundColor = '';
    }

    // Common background properties
    [htmlElement, bodyElement].forEach(el => {
      el.style.backgroundSize = 'cover';
      el.style.backgroundPosition = 'center';
      el.style.backgroundRepeat = 'no-repeat';
      el.style.backgroundAttachment = 'fixed';
    });

    return () => {
      // Cleanup on unmount
      [htmlElement, bodyElement].forEach(el => {
        el.style.backgroundImage = '';
        el.style.backgroundColor = '';
      });
    };
  }, [clockSettings.backgroundColor, clockSettings.backgroundImage]);

  return (
    <MainLayout>
      {/* Alarm Indicator at the top center */}
      <div className={`fixed ${isFullScreen ? 'top-8' : 'top-24'} left-1/2 transform -translate-x-1/2 z-40`}>
        <AlarmIndicator
          alarms={alarms}
          onToggleAlarm={handleToggleAlarm}
          onDeleteAlarm={handleDeleteAlarm}
        />
      </div>

      {/* Main clock area */}
      <div className="w-full h-full flex items-center justify-center p-4">
        <DigitalClock
          showSeconds={clockSettings.showSeconds}
          showWeekday={clockSettings.showWeekday}
          showDate={clockSettings.showDate}
          showWeekNumber={clockSettings.showWeekNumber}
          use12Hours={clockSettings.use12Hours}
          textColor={clockSettings.textColor}
          fontSize={clockSettings.fontSize}
          fontFamily={clockSettings.fontFamily}
          position={clockSettings.position}
        />
      </div>

      {/* Alarm Settings panel - floating in top-right corner */}
      <div className={`fixed ${isFullScreen ? 'top-4' : 'top-20'} right-4 z-50`}>
        <AlarmSettings
          onAddAlarm={handleAddAlarm}
          onTestAlarm={handleTestAlarm}
          existingAlarms={alarms}
        />
      </div>

      <AlarmNotification
        isOpen={isAlarmRinging}
        onClose={handleCloseAlarm}
        onSnooze={handleSnoozeAlarm}
        alarm={activeAlarm}
      />

      {/* Debug info - only show in development and after mount */}
      {process.env.NODE_ENV === 'development' && isMounted && (
        <div className="fixed bottom-4 left-4 z-40 bg-black bg-opacity-50 text-white p-3 rounded-lg text-xs">
          <div>Loaded: {isLoaded ? 'Yes' : 'No'}</div>
          <div>Total alarms: {alarms.length}</div>
          <div>Permanent: {alarms.filter(a => !a.isTemporary).length}</div>
          <div>Temporary: {alarms.filter(a => a.isTemporary).length}</div>
          <div>Queue count: {alarmQueue.length}</div>
          <div>localStorage: {localStorage.getItem("clockApp_alarms")?.length || 0} chars</div>
          <button
            onClick={() => {
              console.log("isLoaded:", isLoaded);
              console.log("Current alarms:", alarms);
              console.log("localStorage alarms:", localStorage.getItem("clockApp_alarms"));
              console.log("All localStorage keys:", Object.keys(localStorage));
            }}
            className="mt-2 px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs"
          >
            Debug Log
          </button>
          <button
            onClick={() => {
              localStorage.removeItem("clockApp_alarms");
              localStorage.removeItem("clockApp_alarms_backup");
              setAlarms([]);
              console.log("Cleared all alarms");
            }}
            className="mt-1 px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-xs w-full"
          >
            Clear All
          </button>
        </div>
      )}
    </MainLayout>
  );
}
